# 📎 邮件附件功能演示指南

## 🎯 功能概述

本项目已完整实现邮件附件的发送和接收功能，包括：

- ✅ **发送带附件的邮件** - 支持多种文件类型
- ✅ **接收邮件附件解析** - 完整解析MIME结构
- ✅ **附件保存和预览** - 自动保存并支持文本预览
- ✅ **多种文件格式支持** - 文本、图片、文档、压缩包等

## 🚀 快速开始

### 1. 配置邮箱账户

首先使用配置助手设置您的邮箱账户：

```bash
python account_config_helper.py
```

选择 "添加新账户" 或 "更新账户密码"，确保：
- ✅ SMTP连接测试成功
- ✅ POP3连接测试成功

### 2. 运行附件演示

```bash
python demo_attachment_email.py
```

演示选项：
- `1` - 仅演示发送带附件的邮件
- `2` - 仅演示接收邮件和附件解析  
- `3` - 完整演示（推荐）

## 📤 发送附件功能

### 通过主程序发送

1. 启动邮件客户端：
   ```bash
   python cli.py
   ```

2. 选择 "发送邮件" → "创建新邮件"

3. 填写邮件信息后，系统会询问是否添加附件

4. 输入附件文件路径，支持多个附件

### 通过演示脚本发送

演示脚本会自动创建测试附件：
- `测试文档.txt` - 文本文件
- `数据表.csv` - CSV数据文件
- `配置.json` - JSON配置文件

## 📥 接收附件功能

### 通过主程序查看

1. 启动邮件客户端：
   ```bash
   python cli.py
   ```

2. 选择 "接收邮件" 获取最新邮件

3. 选择 "查看邮件" → "收件箱"

4. 选择包含附件的邮件查看详情

5. 系统会显示附件信息并询问是否保存

### 附件信息显示

查看邮件时会显示：
```
📎 附件信息 (3 个):
  1. 📄 测试文档.txt
     📊 类型: text/plain
     📏 大小: 1.25 KB
  2. 📄 数据表.csv
     📊 类型: text/csv
     📏 大小: 0.85 KB
  3. 📄 配置.json
     📊 类型: application/json
     📏 大小: 0.95 KB
```

## 🔧 技术实现

### 发送端处理

1. **附件编码**：使用 `MIMEHandler.encode_attachment()` 
2. **MIME构建**：通过 `EmailMimeBuilder` 创建多部分邮件
3. **类型检测**：自动识别文件MIME类型
4. **安全处理**：文件名编码和大小限制

### 接收端处理

1. **MIME解析**：使用 `EmailFormatHandler.parse_mime_message()`
2. **附件提取**：通过 `EmailContentProcessor.extract_content_and_attachments()`
3. **内容解码**：支持Base64、Quoted-Printable等编码
4. **文件保存**：使用 `MIMEHandler.decode_attachment()` 安全保存

### 支持的文件类型

| 类型 | 扩展名            | MIME类型                            | 说明         |
| ---- | ----------------- | ----------------------------------- | ------------ |
| 文本 | .txt, .csv        | text/plain, text/csv                | 支持内容预览 |
| 文档 | .pdf, .doc, .docx | application/pdf, application/msword | 办公文档     |
| 表格 | .xls, .xlsx       | application/vnd.ms-excel            | Excel文件    |
| 图片 | .jpg, .png, .gif  | image/jpeg, image/png               | 图片文件     |
| 压缩 | .zip, .rar        | application/zip                     | 压缩文件     |
| JSON | .json             | application/json                    | 配置文件     |

## 📁 目录结构

```
项目根目录/
├── test_attachments/          # 演示创建的测试附件
│   ├── 测试文档.txt
│   ├── 数据表.csv
│   └── 配置.json
├── received_attachments/      # 接收的附件保存目录
├── attachments/              # 主程序保存的附件
└── demo_attachment_email.py  # 演示脚本
```

## 🛠️ 故障排除

### 发送问题

1. **认证失败**：
   - 确认使用授权码而非登录密码
   - 运行 `python account_config_helper.py` 测试连接

2. **附件过大**：
   - 检查邮件服务商的附件大小限制
   - 通常限制为25MB以下

3. **文件路径错误**：
   - 使用绝对路径或确认相对路径正确
   - 检查文件是否存在且有读取权限

### 接收问题

1. **附件解析失败**：
   - 检查邮件是否为MIME格式
   - 查看日志文件了解详细错误

2. **保存失败**：
   - 确认有写入权限
   - 检查磁盘空间是否充足

3. **编码问题**：
   - 系统会自动处理多种编码格式
   - 如有问题请查看错误日志

## 📝 使用示例

### 完整演示流程

1. **配置账户**：
   ```bash
   python account_config_helper.py
   # 选择 "6. 📝 添加新账户（可见输入）"
   # 或 "2. 🔐 更新账户密码（可见输入）"
   # 然后 "3. 🧪 测试SMTP连接" 确认配置正确
   ```

2. **运行演示**：
   ```bash
   python demo_attachment_email.py
   # 选择 "3" 进行完整演示
   # 输入收件人邮箱地址
   # 确认发送后等待邮件送达
   ```

3. **查看结果**：
   - 检查 `test_attachments/` 目录中的原始文件
   - 检查 `received_attachments/` 目录中的接收文件
   - 对比文件内容确认传输完整性

## 🎉 演示效果

成功运行后您将看到：

1. **发送端**：
   - 自动创建3个测试附件
   - 显示邮件摘要和附件信息
   - 确认发送成功

2. **接收端**：
   - 连接邮箱并获取邮件列表
   - 解析邮件内容和附件信息
   - 自动保存附件并显示预览

3. **文件对比**：
   - 原始文件和接收文件内容完全一致
   - 文件名和大小信息正确保留

这个演示完整展示了邮件客户端的附件处理能力，满足了老师要求的"发送带附件的邮件并展示接收端完整解析"的功能需求。 