{% extends "base.html" %} {% block title %}高级邮箱设置 - {{ app_name }}{%
endblock %} {% block content %}
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cogs me-2"></i>高级邮箱设置</h2>
        <div>
          <a
            href="{{ url_for('mail_config.index') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-arrow-left me-2"></i>返回配置页
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="row justify-content-center">
    <div class="col-lg-10">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-sliders-h me-2"></i>详细配置参数
          </h5>
        </div>
        <div class="card-body">
          <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

            <!-- 显示名称 -->
            <div class="mb-4">
              <label for="mail_display_name" class="form-label"
                >显示名称 *</label
              >
              {{ form.mail_display_name(class="form-control") }}
              <div class="form-text">收件人看到的发件人名称</div>
            </div>

            <div class="row">
              <!-- SMTP配置 -->
              <div class="col-md-6">
                <div class="card border-primary">
                  <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                      <i class="fas fa-paper-plane me-2"></i>SMTP配置 (发送邮件)
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label for="smtp_server" class="form-label"
                        >SMTP服务器 *</label
                      >
                      {{ form.smtp_server(class="form-control") }}
                    </div>

                    <div class="row">
                      <div class="col-6">
                        <div class="mb-3">
                          <label for="smtp_port" class="form-label"
                            >SMTP端口 *</label
                          >
                          {{ form.smtp_port(class="form-control") }}
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="mb-3">
                          <div class="form-check mt-4">
                            {{ form.smtp_use_tls(class="form-check-input") }}
                            <label class="form-check-label" for="smtp_use_tls">
                              使用TLS加密
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="smtp_username" class="form-label"
                        >SMTP用户名 *</label
                      >
                      {{ form.smtp_username(class="form-control") }}
                      <div class="form-text">通常是您的邮箱地址</div>
                    </div>

                    <div class="mb-3">
                      <label for="smtp_password" class="form-label"
                        >SMTP密码 *</label
                      >
                      {{ form.smtp_password(class="form-control") }}
                      <div class="form-text">邮箱密码或应用专用密码</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- POP3配置 -->
              <div class="col-md-6">
                <div class="card border-success">
                  <div class="card-header bg-success text-white">
                    <h6 class="mb-0">
                      <i class="fas fa-inbox me-2"></i>POP3配置 (接收邮件)
                    </h6>
                  </div>
                  <div class="card-body">
                    <div class="mb-3">
                      <label for="pop3_server" class="form-label"
                        >POP3服务器 *</label
                      >
                      {{ form.pop3_server(class="form-control") }}
                    </div>

                    <div class="row">
                      <div class="col-6">
                        <div class="mb-3">
                          <label for="pop3_port" class="form-label"
                            >POP3端口 *</label
                          >
                          {{ form.pop3_port(class="form-control") }}
                        </div>
                      </div>
                      <div class="col-6">
                        <div class="mb-3">
                          <div class="form-check mt-4">
                            {{ form.pop3_use_ssl(class="form-check-input") }}
                            <label class="form-check-label" for="pop3_use_ssl">
                              使用SSL加密
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="mb-3">
                      <label for="pop3_username" class="form-label"
                        >POP3用户名 *</label
                      >
                      {{ form.pop3_username(class="form-control") }}
                      <div class="form-text">通常是您的邮箱地址</div>
                    </div>

                    <div class="mb-3">
                      <label for="pop3_password" class="form-label"
                        >POP3密码 *</label
                      >
                      {{ form.pop3_password(class="form-control") }}
                      <div class="form-text">邮箱密码或应用专用密码</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="d-flex justify-content-between mt-4">
              <div>
                <a
                  href="{{ url_for('mail_config.quick_setup') }}"
                  class="btn btn-outline-primary"
                >
                  <i class="fas fa-magic me-2"></i>使用快速设置
                </a>
              </div>
              <div>{{ form.submit(class="btn btn-success btn-lg") }}</div>
            </div>
          </form>
        </div>
      </div>

      <!-- 配置说明 -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>配置说明</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>常见服务商参数</h6>
              <div class="table-responsive">
                <table class="table table-sm">
                  <thead>
                    <tr>
                      <th>服务商</th>
                      <th>SMTP</th>
                      <th>POP3</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td><strong>Gmail</strong></td>
                      <td>smtp.gmail.com:587 (TLS)</td>
                      <td>pop.gmail.com:995 (SSL)</td>
                    </tr>
                    <tr>
                      <td><strong>Outlook</strong></td>
                      <td>smtp-mail.outlook.com:587 (TLS)</td>
                      <td>outlook.office365.com:995 (SSL)</td>
                    </tr>
                    <tr>
                      <td><strong>QQ邮箱</strong></td>
                      <td>smtp.qq.com:587 (TLS)</td>
                      <td>pop.qq.com:995 (SSL)</td>
                    </tr>
                    <tr>
                      <td><strong>163邮箱</strong></td>
                      <td>smtp.163.com:465 (SSL)</td>
                      <td>pop.163.com:995 (SSL)</td>
                    </tr>
                    <tr>
                      <td><strong>126邮箱</strong></td>
                      <td>smtp.126.com:465 (SSL)</td>
                      <td>pop.126.com:995 (SSL)</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-md-6">
              <h6>安全配置注意事项</h6>
              <ul class="small">
                <li>
                  <strong>Gmail/Outlook:</strong>
                  需要启用2步验证并使用应用专用密码
                </li>
                <li>
                  <strong>QQ邮箱:</strong>
                  需要在设置中开启SMTP/POP3服务并获取授权码
                </li>
                <li>
                  <strong>163/126邮箱:</strong>
                  需要开启客户端授权服务并设置客户端授权密码
                </li>
                <li>
                  <strong>TLS vs SSL:</strong>
                  TLS通常用于587端口，SSL用于465/995端口
                </li>
                <li>
                  <strong>密码:</strong>
                  强烈建议使用应用专用密码或授权码，而不是登录密码
                </li>
              </ul>

              <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <small>
                  <strong>安全提示:</strong>
                  您的密码将被加密存储，但请确保使用专用密码而非主密码。
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // 根据端口号自动设置加密选项
  document.addEventListener("DOMContentLoaded", function () {
    const smtpPortField = document.getElementById("smtp_port");
    const smtpTlsField = document.getElementById("smtp_use_tls");
    const pop3PortField = document.getElementById("pop3_port");
    const pop3SslField = document.getElementById("pop3_use_ssl");

    // SMTP端口变化时自动调整TLS设置
    if (smtpPortField && smtpTlsField) {
      smtpPortField.addEventListener("change", function () {
        const port = parseInt(this.value);
        if (port === 587 || port === 25) {
          smtpTlsField.checked = true; // 587端口通常使用TLS
        } else if (port === 465) {
          smtpTlsField.checked = false; // 465端口通常使用SSL
        }
      });
    }

    // POP3端口变化时自动调整SSL设置
    if (pop3PortField && pop3SslField) {
      pop3PortField.addEventListener("change", function () {
        const port = parseInt(this.value);
        if (port === 995) {
          pop3SslField.checked = true; // 995端口通常使用SSL
        } else if (port === 110) {
          pop3SslField.checked = false; // 110端口通常不使用SSL
        }
      });
    }
  });
</script>
{% endblock %}
