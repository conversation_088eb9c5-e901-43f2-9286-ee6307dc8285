# 📧 邮件撤回功能使用指南

## 🎯 功能概述

**邮件撤回**是一个**服务器端操作**，允许发件人撤回已发送的邮件，让收件人无法再查看邮件内容。

### 🔄 **撤回 vs 删除的重要区别**

| 操作类型       | **撤回邮件**                   | **删除邮件**           |
| -------------- | ------------------------------ | ---------------------- |
| **影响范围**   | 服务器端，影响所有用户         | 本地操作，仅影响操作者 |
| **发件人视角** | 邮件标记为"已撤回"             | 邮件从列表中消失       |
| **收件人视角** | 邮件被隐藏，无法查看           | 邮件仍然可见           |
| **典型场景**   | 发错了邮件，需要防止收件人看到 | 自己不想看到某些邮件   |

### ⏰ **撤回限制条件**

1. **时间限制**：只能撤回24小时内发送的邮件
2. **权限限制**：只能撤回自己发送的邮件  
3. **状态限制**：已撤回的邮件无法再次撤回

## 🚀 使用方法

### **方法1：通过查看邮件菜单**

1. 启动邮件客户端：`python main.py`
2. 选择 `2. 📋 查看邮件`
3. 选择 `6. 🔙 撤回邮件`
4. 系统会显示您可撤回的邮件列表
5. 输入邮件ID确认撤回

### **方法2：直接查看已发送邮件**

1. 进入 `2. 📋 查看邮件` → `2. 📤 已发送`
2. 在已发送邮件列表中会显示撤回状态：
   - `✅已读` / `📬未读`：正常状态
   - `🔙已撤回`：已撤回的邮件
   - `[已撤回] 邮件主题`：在主题前显示撤回标记

## 🛡️ 技术实现

### **数据库结构**

新增了以下字段来支持撤回功能：

```sql
-- emails 表和 sent_emails 表都新增了：
ALTER TABLE emails ADD COLUMN is_recalled INTEGER DEFAULT 0;
ALTER TABLE emails ADD COLUMN recalled_at TEXT;
ALTER TABLE emails ADD COLUMN recalled_by TEXT;

ALTER TABLE sent_emails ADD COLUMN is_recalled INTEGER DEFAULT 0;
ALTER TABLE sent_emails ADD COLUMN recalled_at TEXT;
ALTER TABLE sent_emails ADD COLUMN recalled_by TEXT;
```

### **核心API**

```python
# 撤回邮件
result = db.recall_email(message_id, user_email)

# 检查撤回权限
permission = db.email_repo.can_recall_email(message_id, user_email)

# 获取可撤回邮件列表
recallable_emails = db.get_recallable_emails(user_email)
```

### **邮件过滤逻辑**

- **收件人视角**：`include_recalled=False`（默认），已撤回邮件被隐藏
- **发件人视角**：已撤回邮件显示为"🔙已撤回"状态

## 📋 使用示例

### **场景1：撤回发错的邮件**

```
情况：Alice 误将内部文档发给了外部客户
操作：
1. Alice 进入"撤回邮件"菜单
2. 选择要撤回的邮件
3. 确认撤回操作
结果：客户无法再查看该邮件内容
```

### **场景2：撤回包含错误信息的邮件**

```
情况：Bob 发送了包含错误数据的报告
操作：
1. Bob 在"已发送"列表中看到邮件
2. 使用撤回功能撤回邮件
3. 重新发送正确的报告
结果：收件人只能看到最新的正确报告
```

## ⚠️ 注意事项

1. **不是100%可靠**：如果收件人已经下载或转发了邮件，撤回无法阻止
2. **时间窗口**：24小时后自动失去撤回权限
3. **网络同步**：在分布式环境中可能存在同步延迟
4. **审计记录**：撤回操作会记录操作时间和操作人

## 🔧 故障排除

### **无法撤回邮件**

**可能原因和解决方案：**

1. **"邮件发送超过24小时"**
   - 解决：撤回时间窗口已过，无法撤回

2. **"只能撤回自己发送的邮件"**
   - 解决：检查当前登录账户是否为发件人

3. **"邮件已经撤回过了"**
   - 解决：邮件已处于撤回状态，无需重复操作

4. **"邮件不存在或不是您发送的邮件"**
   - 解决：检查邮件ID是否正确，确认邮件存在

### **撤回后收件人仍能看到邮件**

1. 检查收件人是否刷新了邮件列表
2. 确认撤回操作是否成功（查看已发送邮件状态）
3. 联系系统管理员检查数据库同步状态

## 📈 功能扩展

未来可以考虑的增强功能：

1. **撤回通知**：向收件人发送撤回通知
2. **批量撤回**：一次撤回多封邮件
3. **定时撤回**：设置邮件自动撤回时间
4. **撤回审批**：重要邮件撤回需要管理员批准
5. **撤回统计**：提供撤回操作的数据分析

---

🎉 **邮件撤回功能现已完整实现！** 让您可以更安全、更灵活地管理邮件发送。 