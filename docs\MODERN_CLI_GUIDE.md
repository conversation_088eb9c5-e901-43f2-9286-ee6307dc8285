# 现代化邮件客户端CLI使用指南

## 概述

现代化邮件客户端CLI v2.0 提供了全新的用户友好界面，支持多账户管理、主流邮件服务商预设配置、安全的密码存储等功能。

## 主要特性

### 🔧 账户管理系统
- **多账户支持**: 可以配置和管理多个邮箱账户
- **快速切换**: 在不同账户间快速切换
- **安全存储**: 密码采用加密存储，保护用户隐私
- **配置持久化**: 账户配置自动保存，下次启动时自动加载

### 📮 预设配置支持
支持以下主流邮件服务商的自动配置：
- **QQ邮箱** (qq.com)
- **Gmail** (gmail.com, googlemail.com)
- **163邮箱** (163.com)
- **126邮箱** (126.com)
- **Outlook/Hotmail** (outlook.com, hotmail.com, live.com, msn.com)
- **Yahoo邮箱** (yahoo.com, yahoo.cn)
- **自定义服务器** (手动配置)

### 🔐 安全特性
- **主密码保护**: 使用主密码保护所有账户配置
- **密码加密**: 使用 PBKDF2 + Fernet 加密算法
- **本地存储**: 所有配置存储在本地，不上传到云端

## 快速开始

### 1. 启动程序
```bash
python cli.py
```

### 2. 首次使用
首次启动时，程序会提示您设置邮箱账户：
- 选择 "是" 进入账户设置向导
- 按照提示输入邮箱地址
- 程序会自动识别邮件服务商
- 输入密码/授权码完成配置

### 3. 主菜单功能
- **📤 发送邮件**: 发送新邮件
- **📥 接收邮件**: 接收邮件到本地
- **📋 查看邮件列表**: 浏览已接收的邮件
- **🔍 搜索邮件**: 搜索特定邮件
- **⚙️ 账户设置**: 管理邮箱账户
- **📊 系统状态**: 查看系统和账户状态

## 详细功能说明

### 账户设置

#### 添加新账户
1. 进入 "账户设置" → "添加新账户"
2. 输入邮箱地址（如：<EMAIL>）
3. 程序自动识别服务商并显示设置说明
4. 输入账户名称和显示名称
5. 输入密码/授权码
6. 确认配置并保存

#### 管理现有账户
- **查看账户信息**: 显示详细的账户配置
- **编辑账户**: 修改显示名称和备注
- **更改密码**: 更新密码/授权码
- **测试连接**: 验证账户配置是否正确
- **导出配置**: 备份账户配置到文件
- **删除账户**: 永久删除账户配置

#### 切换当前账户
- 在多个账户间快速切换
- 程序会记住最后使用的账户作为默认账户

### 高级设置
- **导出所有账户配置**: 批量备份所有账户
- **导入账户配置**: 从备份文件恢复账户
- **重置主密码**: 清除所有配置并重新设置
- **清理配置文件**: 清理无效配置项

## 邮件服务商配置指南

### QQ邮箱设置
1. 登录QQ邮箱网页版
2. 进入 "设置" → "账户"
3. 开启 "SMTP/POP3/IMAP" 服务
4. 生成授权码
5. 在客户端中使用授权码作为密码

### Gmail设置
1. 登录Gmail网页版
2. 进入Google账户设置
3. 开启两步验证
4. 生成应用专用密码
5. 在客户端中使用应用专用密码

### 163/126邮箱设置
1. 登录邮箱网页版
2. 进入 "设置" → "POP3/SMTP/IMAP"
3. 开启相应服务
4. 设置客户端授权密码
5. 在客户端中使用授权密码

### Outlook/Yahoo设置
1. 登录邮箱网页版
2. 进入安全设置
3. 开启两步验证（推荐）
4. 生成应用密码
5. 在客户端中使用应用密码

## 故障排除

### 常见问题

#### 1. 无法连接到邮件服务器
**可能原因**:
- 服务器地址或端口错误
- 网络连接问题
- 防火墙阻止连接

**解决方案**:
- 检查服务器配置是否正确
- 确认网络连接正常
- 检查防火墙设置

#### 2. 认证失败
**可能原因**:
- 密码/授权码错误
- 未开启SMTP/POP3服务
- 使用了登录密码而非授权码

**解决方案**:
- 确认使用正确的授权码
- 检查邮箱服务是否已开启
- 重新生成授权码

#### 3. 程序启动失败
**可能原因**:
- 缺少依赖包
- Python版本不兼容
- 配置文件损坏

**解决方案**:
- 安装所需依赖: `pip install -r requirements.txt`
- 确认Python版本 >= 3.8
- 删除配置目录重新设置

### 配置文件位置
- **Windows**: `C:\Users\<USER>\.email_client\`
- **Linux/Mac**: `~/.email_client/`

### 日志文件
程序运行日志保存在项目的 `logs/` 目录下，可用于问题诊断。

## 安全建议

1. **定期备份配置**: 使用导出功能定期备份账户配置
2. **使用强主密码**: 设置复杂的主密码保护账户信息
3. **及时更新授权码**: 定期更新邮箱授权码
4. **谨慎分享配置**: 不要将包含密码的配置文件分享给他人

## 技术支持

如遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查程序日志文件
3. 参考各邮件服务商的官方帮助文档
4. 确认网络连接和防火墙设置

## 更新日志

### v2.0 (当前版本)
- ✨ 全新的现代化用户界面
- 🔧 多账户管理系统
- 📮 主流邮件服务商预设配置
- 🔐 安全的密码加密存储
- 📊 系统状态监控
- 📖 详细的帮助和说明

### v1.0
- 基础的邮件收发功能
- 简单的命令行界面 