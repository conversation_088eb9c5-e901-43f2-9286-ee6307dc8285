{% extends "base.html" %} {% block title %}邮箱配置 - {{ app_name }}{% endblock
%} {% block content %}
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-cog me-2"></i>邮箱配置</h2>
        <div>
          <a
            href="{{ url_for('main.dashboard') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-arrow-left me-2"></i>返回仪表板
          </a>
        </div>
      </div>
    </div>
  </div>

  {% if has_config %}
  <!-- 已配置状态 -->
  <div class="row">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">
            <i class="fas fa-check-circle me-2"></i>邮箱已配置
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>SMTP配置 (发送邮件)</h6>
              <table class="table table-sm table-borderless">
                <tr>
                  <td><strong>显示名称:</strong></td>
                  <td>{{ current_user.mail_display_name or '未设置' }}</td>
                </tr>
                <tr>
                  <td><strong>服务器:</strong></td>
                  <td>{{ current_user.smtp_server }}</td>
                </tr>
                <tr>
                  <td><strong>端口:</strong></td>
                  <td>{{ current_user.smtp_port }}</td>
                </tr>
                <tr>
                  <td><strong>用户名:</strong></td>
                  <td>{{ current_user.smtp_username }}</td>
                </tr>
                <tr>
                  <td><strong>加密:</strong></td>
                  <td>{{ 'TLS' if current_user.smtp_use_tls else 'SSL' }}</td>
                </tr>
                <tr>
                  <td><strong>状态:</strong></td>
                  <td>
                    {% if current_user.smtp_configured %}
                    <span class="badge bg-success">已配置</span>
                    {% else %}
                    <span class="badge bg-danger">未配置</span>
                    {% endif %}
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h6>POP3配置 (接收邮件)</h6>
              <table class="table table-sm table-borderless">
                <tr>
                  <td><strong>服务器:</strong></td>
                  <td>{{ current_user.pop3_server }}</td>
                </tr>
                <tr>
                  <td><strong>端口:</strong></td>
                  <td>{{ current_user.pop3_port }}</td>
                </tr>
                <tr>
                  <td><strong>用户名:</strong></td>
                  <td>{{ current_user.pop3_username }}</td>
                </tr>
                <tr>
                  <td><strong>加密:</strong></td>
                  <td>{{ 'SSL' if current_user.pop3_use_ssl else '无' }}</td>
                </tr>
                <tr>
                  <td><strong>状态:</strong></td>
                  <td>
                    {% if current_user.pop3_configured %}
                    <span class="badge bg-success">已配置</span>
                    {% else %}
                    <span class="badge bg-danger">未配置</span>
                    {% endif %}
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="card-footer">
          <div class="btn-group" role="group">
            <a
              href="{{ url_for('mail_config.test_config') }}"
              class="btn btn-primary"
            >
              <i class="fas fa-paper-plane me-2"></i>测试配置
            </a>
            <a
              href="{{ url_for('mail_config.advanced_setup') }}"
              class="btn btn-warning"
            >
              <i class="fas fa-edit me-2"></i>修改配置
            </a>
            <button
              type="button"
              class="btn btn-danger"
              data-bs-toggle="modal"
              data-bs-target="#deleteModal"
            >
              <i class="fas fa-trash me-2"></i>删除配置
            </button>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>配置说明</h6>
        </div>
        <div class="card-body">
          <p><strong>注意事项：</strong></p>
          <ul class="small">
            <li>Gmail/Outlook 用户需要使用应用专用密码</li>
            <li>QQ邮箱需要开启SMTP/POP3服务</li>
            <li>163/126邮箱需要设置客户端授权密码</li>
            <li>配置成功后即可收发真实邮件</li>
          </ul>
          <p><strong>功能：</strong></p>
          <ul class="small">
            <li>
              <i class="fas fa-check text-success me-1"></i> 发送邮件到任何邮箱
            </li>
            <li><i class="fas fa-check text-success me-1"></i> 接收外部邮件</li>
            <li><i class="fas fa-check text-success me-1"></i> 垃圾邮件过滤</li>
            <li><i class="fas fa-check text-success me-1"></i> 附件支持</li>
          </ul>
        </div>
      </div>
    </div>
  </div>

  {% else %}
  <!-- 未配置状态 -->
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header bg-warning text-dark">
          <h5 class="mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>邮箱未配置
          </h5>
        </div>
        <div class="card-body text-center">
          <i class="fas fa-envelope fa-4x text-muted mb-4"></i>
          <h4>配置您的真实邮箱</h4>
          <p class="text-muted mb-4">
            配置您的真实邮箱账户，即可像QQ邮箱客户端一样发送和接收真实邮件。
            支持Gmail、Outlook、QQ邮箱、163邮箱等主流邮件服务商。
          </p>

          <div class="d-grid gap-3 d-md-block">
            <a
              href="{{ url_for('mail_config.quick_setup') }}"
              class="btn btn-primary btn-lg me-md-3"
            >
              <i class="fas fa-rocket me-2"></i>快速配置
            </a>
            <a
              href="{{ url_for('mail_config.advanced_setup') }}"
              class="btn btn-outline-secondary btn-lg"
            >
              <i class="fas fa-cogs me-2"></i>高级配置
            </a>
          </div>
        </div>
      </div>

      <!-- 功能介绍 -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0">
            <i class="fas fa-star me-2"></i>配置后可使用的功能
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>
                <i class="fas fa-paper-plane text-primary me-2"></i>邮件发送
              </h6>
              <ul class="small">
                <li>发送邮件到任何外部邮箱</li>
                <li>支持HTML格式和附件</li>
                <li>自动SMTP配置</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6><i class="fas fa-inbox text-success me-2"></i>邮件接收</h6>
              <ul class="small">
                <li>接收外部邮件到本地</li>
                <li>支持POP3协议</li>
                <li>自动垃圾邮件过滤</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">确认删除</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <p>确定要删除邮箱配置吗？删除后您将无法发送和接收邮件。</p>
        <p class="text-danger"><strong>注意：此操作不可撤销！</strong></p>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          取消
        </button>
        <form
          method="POST"
          action="{{ url_for('mail_config.delete_config') }}"
          style="display: inline"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <button type="submit" class="btn btn-danger">确定删除</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
