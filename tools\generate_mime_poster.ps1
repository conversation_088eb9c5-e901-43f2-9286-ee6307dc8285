# MIME 结构图表生成脚本
# 适用于 Windows PowerShell

param(
    [string]$OutputFormat = "png",  # png, pdf, both
    [int]$DPI = 300,               # 分辨率设置
    [string]$Size = "A0"           # A0, A1, A2 等
)

Write-Host "=== MIME 结构图表生成工具 ===" -ForegroundColor Cyan
Write-Host "生成符合学术会议标准的高质量海报" -ForegroundColor Yellow

# 检查 Inkscape 是否已安装
$inkscapePath = "C:\Program Files\Inkscape\bin\inkscape.exe"
if (-not (Test-Path $inkscapePath)) {
    Write-Host "未检测到 Inkscape，正在安装..." -ForegroundColor Yellow
    
    # 检查 winget 是否可用
    try {
        winget --version | Out-Null
        Write-Host "使用 winget 安装 Inkscape..." -ForegroundColor Green
        winget install Inkscape.Inkscape --silent
    }
    catch {
        Write-Host "winget 不可用，请手动下载安装 Inkscape:" -ForegroundColor Red
        Write-Host "https://inkscape.org/release/" -ForegroundColor Blue
        exit 1
    }
}

# 确保输出目录存在
$outputDir = "docs/generated_posters"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force
    Write-Host "创建输出目录: $outputDir" -ForegroundColor Green
}

# SVG 源文件路径
$svgFile = "docs/MIME_Structure_Diagram.svg"
if (-not (Test-Path $svgFile)) {
    Write-Host "错误: 找不到源 SVG 文件 $svgFile" -ForegroundColor Red
    exit 1
}

# 根据尺寸设置输出参数
$sizeConfig = @{
    "A0" = @{ width = 9933; height = 7016 }   # 841×594mm @ 300DPI
    "A1" = @{ width = 7016; height = 4961 }   # 594×420mm @ 300DPI  
    "A2" = @{ width = 4961; height = 3508 }   # 420×297mm @ 300DPI
}

if (-not $sizeConfig.ContainsKey($Size)) {
    Write-Host "错误: 不支持的尺寸 $Size。支持的尺寸: A0, A1, A2" -ForegroundColor Red
    exit 1
}

$config = $sizeConfig[$Size]
$width = [math]::Round($config.width * $DPI / 300)
$height = [math]::Round($config.height * $DPI / 300)

Write-Host "配置参数:" -ForegroundColor Cyan
Write-Host "  尺寸: $Size" -ForegroundColor White
Write-Host "  分辨率: $DPI DPI" -ForegroundColor White
Write-Host "  输出尺寸: ${width}×${height} 像素" -ForegroundColor White
Write-Host "  输出格式: $OutputFormat" -ForegroundColor White

# 生成时间戳
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"

# 生成 PNG 格式
if ($OutputFormat -eq "png" -or $OutputFormat -eq "both") {
    $pngFile = "$outputDir/MIME_Structure_Poster_${Size}_${DPI}DPI_$timestamp.png"
    
    Write-Host "正在生成 PNG 文件..." -ForegroundColor Yellow
    
    $pngArgs = @(
        "--export-type=png",
        "--export-dpi=$DPI",
        "--export-width=$width",
        "--export-height=$height",
        "--export-filename=`"$pngFile`"",
        "`"$svgFile`""
    )
    
    try {
        & $inkscapePath @pngArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Host "PNG 文件生成成功: $pngFile" -ForegroundColor Green
            
            # 显示文件信息
            $fileInfo = Get-Item $pngFile
            $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
            Write-Host "  文件大小: $fileSizeMB MB" -ForegroundColor White
        } else {
            Write-Host "PNG 生成失败，退出代码: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "PNG 生成时发生错误: $_" -ForegroundColor Red
    }
}

# 生成 PDF 格式
if ($OutputFormat -eq "pdf" -or $OutputFormat -eq "both") {
    $pdfFile = "$outputDir/MIME_Structure_Poster_${Size}_$timestamp.pdf"
    
    Write-Host "正在生成 PDF 文件..." -ForegroundColor Yellow
    
    $pdfArgs = @(
        "--export-type=pdf",
        "--export-area-page",
        "--export-filename=`"$pdfFile`"",
        "`"$svgFile`""
    )
    
    try {
        & $inkscapePath @pdfArgs
        if ($LASTEXITCODE -eq 0) {
            Write-Host "PDF 文件生成成功: $pdfFile" -ForegroundColor Green
            
            # 显示文件信息
            $fileInfo = Get-Item $pdfFile
            $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
            Write-Host "  文件大小: $fileSizeMB MB" -ForegroundColor White
        } else {
            Write-Host "PDF 生成失败，退出代码: $LASTEXITCODE" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "PDF 生成时发生错误: $_" -ForegroundColor Red
    }
}

Write-Host "`n=== 生成完成 ===" -ForegroundColor Cyan
Write-Host "输出目录: $outputDir" -ForegroundColor Blue

# 显示打印指南
Write-Host "`n=== 打印指南 ===" -ForegroundColor Cyan
Write-Host "学术会议海报打印建议:" -ForegroundColor Yellow
Write-Host "• 推荐纸张: 铜版纸或哑光纸 (200-250gsm)" -ForegroundColor White
Write-Host "• 打印质量: 300 DPI 或更高" -ForegroundColor White
Write-Host "• 颜色模式: CMYK 模式打印" -ForegroundColor White
Write-Host "• 查看距离: 设计为 1.5-2 米观看距离" -ForegroundColor White
Write-Host "• 展示环境: 室内照明良好的会议场所" -ForegroundColor White

# 提供使用示例
Write-Host "`n=== 使用示例 ===" -ForegroundColor Cyan
Write-Host "生成不同格式和尺寸的示例:" -ForegroundColor Yellow
Write-Host "  .\tools\generate_mime_poster.ps1 -OutputFormat both -DPI 300 -Size A0" -ForegroundColor Green
Write-Host "  .\tools\generate_mime_poster.ps1 -OutputFormat png -DPI 150 -Size A1" -ForegroundColor Green
Write-Host "  .\tools\generate_mime_poster.ps1 -OutputFormat pdf -Size A2" -ForegroundColor Green 