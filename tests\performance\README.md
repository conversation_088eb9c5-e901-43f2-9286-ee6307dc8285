# 邮件系统并发测试

## 概述

简化的邮件系统并发测试工具，基于现有的POP3多客户端连接功能，添加SMTP发送功能，测试50个用户并发发送带编号邮件的正确性。

## 测试脚本

### `test_high_concurrency.py` - 简化版并发测试

**功能特点：**
- ✅ 基于现有的多客户端连接代码
- ✅ 创建50个测试用户 (testuser001 到 testuser050)
- ✅ 每个用户发送带编号的邮件 (测试邮件#001 到 #050)
- ✅ 使用不同端口避免权限问题 (SMTP:2526, POP3:8111)
- ✅ 验证用户数字和内容匹配
- ✅ 保存结果到test_output目录

**测试流程：**
1. 启动本地SMTP服务器 (localhost:2526) 和POP3服务器 (localhost:8111)
2. 创建50个测试用户
3. 50个用户并发发送带编号邮件 (每个用户发送给自己)
4. 50个用户并发接收邮件
5. 验证每个用户收到的邮件编号是否正确
6. 保存详细结果到test_output目录

**运行方式：**
```bash
python tests/performance/test_high_concurrency.py
```

## 测试结果

测试完成后会在 `test_output` 目录生成：
- `send_results_YYYYMMDD_HHMMSS.json` - 发送结果
- `receive_results_YYYYMMDD_HHMMSS.json` - 接收结果  
- `email_001_YYYYMMDD_HHMMSS.txt` - 用户001的邮件内容
- `email_002_YYYYMMDD_HHMMSS.txt` - 用户002的邮件内容
- ... (每个用户一个文件)

## 验证方法

1. 查看控制台输出的匹配率
2. 检查test_output目录中的邮件文件
3. 验证每个邮件文件中的用户编号是否正确对应

例如：`email_001_*.txt` 应该包含 "用户编号: 001"，`email_050_*.txt` 应该包含 "用户编号: 050"

## 成功示例

```
简化版并发邮件测试
==================================================
测试 50 个用户并发发送带编号邮件
==================================================
🚀 启动测试服务器...
📤 启动SMTP服务器 (localhost:2526)
✅ SMTP服务器已启动
📥 启动POP3服务器 (localhost:8111)
✅ POP3服务器已启动
✅ 所有服务器启动成功！

👥 创建 50 个测试用户...
  已创建: 10/50
  已创建: 20/50
  已创建: 30/50
  已创建: 40/50
  已创建: 50/50
✅ 成功创建 50 个用户

📤 并发发送测试...

⏱️  等待邮件投递...

📥 并发接收测试...

📁 测试结果已保存到: test_output

=== 验证结果 ===
发送成功: 50/50
接收成功: 50/50
内容匹配: 50/50
匹配率: 100.0%

🎉 测试通过！邮件内容匹配正确

🧹 清理服务器...

✅ 并发测试成功！
📁 查看 test_output 目录了解详细结果 