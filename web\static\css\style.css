/* 邮件客户端Web界面自定义样式 */

/* 全局样式 */
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
  font-weight: bold;
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 10px;
  transition: transform 0.2s ease-in-out;
}

.card:hover {
  transform: translateY(-2px);
}

/* 按钮样式 */
.btn {
  border-radius: 6px;
  font-weight: 500;
}

/* 表单样式 */
.form-control {
  border-radius: 6px;
  border: 1px solid #dee2e6;
}

.form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 邮件列表样式 */
.email-item {
  border-bottom: 1px solid #e9ecef;
  padding: 15px;
  transition: background-color 0.2s;
}

.email-item:hover {
  background-color: #f8f9fa;
}

.email-item.unread {
  background-color: #fff3cd;
  font-weight: 600;
}

.email-item.spam {
  background-color: #f8d7da;
}

/* 邮件内容样式 */
.email-content {
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.email-header {
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

/* 附件样式 */
.attachment-item {
  display: inline-block;
  background-color: #e9ecef;
  padding: 8px 12px;
  border-radius: 6px;
  margin: 5px;
  text-decoration: none;
  color: #495057;
}

.attachment-item:hover {
  background-color: #dee2e6;
  color: #495057;
}

/* 状态标签 */
.status-badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .container-fluid {
    padding-left: 10px;
    padding-right: 10px;
  }

  .card-body {
    padding: 1rem;
  }
}

/* CKEditor样式调整 */
.ckeditor {
  min-height: 300px;
}

/* 加载动画 */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(13, 110, 253, 0.3);
  border-radius: 50%;
  border-top-color: #0d6efd;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 工具提示样式 */
.tooltip-inner {
  background-color: #212529;
  color: white;
  border-radius: 4px;
}

/* 分页样式 */
.pagination {
  justify-content: center;
}

.page-link {
  border-radius: 6px;
  margin: 0 2px;
  border: 1px solid #dee2e6;
}

/* 搜索框样式 */
.search-box {
  position: relative;
}

.search-box .form-control {
  padding-right: 40px;
}

.search-box .search-icon {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}
