{% extends "base.html" %}

{% block title %}邮件搜索{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-search me-2"></i>邮件搜索</h2>
        <a href="{{ url_for('main.dashboard') }}" class="btn btn-outline-secondary">
          <i class="fas fa-arrow-left me-2"></i>返回仪表板
        </a>
      </div>

      <!-- 搜索表单 -->
      <div class="card mb-4">
        <div class="card-body">
          <form method="GET" action="{{ url_for('email.search') }}">
            <div class="input-group">
              <input
                type="text"
                class="form-control"
                name="q"
                value="{{ query }}"
                placeholder="搜索邮件主题、发件人或内容..."
                autofocus
              />
              <button class="btn btn-primary" type="submit">
                <i class="fas fa-search me-2"></i>搜索
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- 搜索结果 -->
      {% if query %}
        <div class="card">
          <div class="card-header">
            <h5 class="mb-0">
              搜索结果: "{{ query }}" 
              {% if results %}
                <span class="badge bg-primary">{{ results|length }} 封邮件</span>
              {% else %}
                <span class="badge bg-secondary">无结果</span>
              {% endif %}
            </h5>
          </div>
          
          {% if results %}
            <div class="card-body p-0">
              <div class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th style="width: 50px">类型</th>
                      <th style="width: 200px">发件人/收件人</th>
                      <th>主题</th>
                      <th style="width: 150px">日期</th>
                      <th style="width: 80px">大小</th>
                      <th style="width: 100px">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for email in results %}
                    <tr>
                      <td>
                        {% if email.get('email_type') == 'sent' %}
                          <span class="badge bg-success">已发送</span>
                        {% else %}
                          <span class="badge bg-info">接收</span>
                        {% endif %}
                      </td>
                      <td>
                        {% if email.get('email_type') == 'sent' %}
                          <small class="text-muted">收件人:</small><br>
                          {{ email.to_addrs|join(', ') if email.to_addrs else '未知' }}
                        {% else %}
                          <small class="text-muted">发件人:</small><br>
                          {{ email.from_addr or '未知' }}
                        {% endif %}
                      </td>
                      <td>
                        <div class="d-flex align-items-center">
                          {% if not email.get('is_read', True) %}
                            <i class="fas fa-circle text-primary me-2" style="font-size: 8px"></i>
                          {% endif %}
                          {% if email.get('has_attachments') %}
                            <i class="fas fa-paperclip text-muted me-2"></i>
                          {% endif %}
                          <span>{{ email.subject or '(无主题)' }}</span>
                        </div>
                      </td>
                      <td>
                        {% if email.date %}
                          {% if email.date.strftime %}
                            {{ email.date.strftime('%m-%d %H:%M') }}
                          {% else %}
                            {{ email.date }}
                          {% endif %}
                        {% else %}
                          未知
                        {% endif %}
                      </td>
                      <td>
                        <small class="text-muted">
                          {{ (email.size / 1024)|round(2) }} KB
                        </small>
                      </td>
                      <td>
                        {% if email.get('email_type') == 'sent' %}
                          <a
                            href="{{ url_for('email.view_sent', message_id=email.message_id) }}"
                            class="btn btn-sm btn-outline-primary"
                            title="查看邮件"
                          >
                            <i class="fas fa-eye"></i>
                          </a>
                        {% else %}
                          <a
                            href="{{ url_for('email.view', message_id=email.message_id) }}"
                            class="btn btn-sm btn-outline-primary"
                            title="查看邮件"
                          >
                            <i class="fas fa-eye"></i>
                          </a>
                        {% endif %}
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            </div>
          {% else %}
            <div class="card-body text-center py-5">
              <i class="fas fa-search fa-3x text-muted mb-3"></i>
              <h5 class="text-muted">没有找到匹配的邮件</h5>
              <p class="text-muted">请尝试使用不同的关键词搜索</p>
            </div>
          {% endif %}
        </div>
      {% else %}
        <!-- 搜索提示 -->
        <div class="card">
          <div class="card-body text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">开始搜索邮件</h5>
            <p class="text-muted">
              输入关键词搜索邮件主题、发件人或内容<br>
              支持搜索接收邮件和已发送邮件
            </p>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<style>
.table td {
  vertical-align: middle;
}

.table td:first-child {
  border-left: none;
}

.table td:last-child {
  border-right: none;
}

.input-group .form-control:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.badge {
  font-size: 0.75em;
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.025);
}
</style>
{% endblock %}
