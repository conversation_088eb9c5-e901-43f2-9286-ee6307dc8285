<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>{% block title %}{{ app_name }}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <!-- Font Awesome -->
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      rel="stylesheet"
    />
    <!-- 自定义CSS -->
    <link
      href="{{ url_for('static', filename='css/style.css') }}"
      rel="stylesheet"
    />

    {% block head %}{% endblock %}
  </head>
  <body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
      <div class="container">
        <a class="navbar-brand" href="{{ url_for('main.index') }}">
          <i class="fas fa-envelope me-2"></i>{{ app_name }}
        </a>

        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarNav"
        >
          <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
          <ul class="navbar-nav me-auto">
            {% if current_user.is_authenticated %}
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-tachometer-alt me-1"></i>仪表板
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('email.inbox') }}">
                <i class="fas fa-inbox me-1"></i>收件箱
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('email.sent') }}">
                <i class="fas fa-paper-plane me-1"></i>发件箱
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="{{ url_for('email.compose') }}">
                <i class="fas fa-edit me-1"></i>写邮件
              </a>
            </li>
            {% endif %}
          </ul>

          <ul class="navbar-nav">
            {% if current_user.is_authenticated %}
            <li class="nav-item dropdown">
              <a
                class="nav-link dropdown-toggle"
                href="#"
                id="navbarDropdown"
                role="button"
                data-bs-toggle="dropdown"
              >
                <i class="fas fa-user me-1"></i>{{ current_user.email if
                hasattr(current_user, 'email') else current_user.username }}
              </a>
              <ul class="dropdown-menu">
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('email_auth.email_login') }}"
                  >
                    <i class="fas fa-user-cog me-1"></i>邮箱设置
                  </a>
                </li>
                <li><hr class="dropdown-divider" /></li>
                <li>
                  <a
                    class="dropdown-item"
                    href="{{ url_for('email_auth.logout') }}"
                  >
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                  </a>
                </li>
              </ul>
            </li>
            {% else %}
            <li class="nav-item">
              <a
                class="nav-link"
                href="{{ url_for('email_auth.email_login') }}"
              >
                <i class="fas fa-sign-in-alt me-1"></i>邮箱登录
              </a>
            </li>
            {% endif %}
          </ul>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="container-fluid mt-4">
      <!-- Flash消息 -->
      {% with messages = get_flashed_messages(with_categories=true) %} {% if
      messages %}
      <div class="row">
        <div class="col-12">
          {% for category, message in messages %}
          <div
            class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show"
            role="alert"
          >
            {{ message }}
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="alert"
            ></button>
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %} {% endwith %}

      <!-- 页面内容 -->
      {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
      <div class="container">
        <p>&copy; 2024 {{ app_name }} v{{ app_version }}. CS3611 项目作品.</p>
      </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- CKEditor (如果需要) -->
    {% block scripts %}{% endblock %}
  </body>
</html>
