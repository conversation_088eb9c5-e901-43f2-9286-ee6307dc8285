# POP3协议参考文档

## 简介

POP3 (Post Office Protocol - Version 3) 是一种用于从服务器获取电子邮件的标准协议，最新规范在RFC 1939中定义，该规范于1996年5月发布。POP3设计用于允许用户从服务器下载邮件并在本地管理，而不需要持续连接到服务器。

## 基本概念

POP3是一个客户端-服务器协议，其中接收方作为客户端，邮件服务器作为服务器。POP3通信基于简单的文本命令和响应，通常使用TCP端口110（标准）或995（SSL加密）。

## 协议状态

POP3会话分为三个主要状态：

1. **授权状态(AUTHORIZATION)**：建立连接后的初始状态，客户端需要验证用户身份
2. **事务状态(TRANSACTION)**：身份验证成功后进入此状态，允许客户端发出命令管理邮件
3. **更新状态(UPDATE)**：客户端发出QUIT命令后进入此状态，服务器释放被标记删除的邮件资源并关闭连接

## 通信流程

POP3通信流程主要包括以下步骤：

1. **建立连接**：客户端连接到POP3服务器
2. **问候**：服务器发送欢迎信息
3. **认证**：客户端提供用户名和密码进行身份验证
4. **获取邮件信息**：列出可用邮件并获取所需信息
5. **下载邮件内容**：下载需要的邮件
6. **标记要删除的邮件**（可选）
7. **结束会话**：QUIT命令关闭连接并执行更新

### 连接建立示例

```
C: <建立TCP连接到服务器端口110>
S: +OK POP3 server ready <<EMAIL>>
C: USER mrose
S: +OK User accepted
C: PASS secret
S: +OK Pass accepted
```

## 主要POP3命令

### 授权状态命令

| 命令 | 描述            | 语法                       | 示例                            |
| ---- | --------------- | -------------------------- | ------------------------------- |
| USER | 指定用户名      | `USER <username>`          | `USER john`                     |
| PASS | 提供密码        | `PASS <password>`          | `PASS secret123`                |
| APOP | 带MD5认证的登录 | `APOP <username> <digest>` | `APOP john c4c9334bac560ecc...` |
| QUIT | 退出会话        | `QUIT`                     | `QUIT`                          |

### 事务状态命令

| 命令 | 描述                   | 语法            | 示例               |
| ---- | ---------------------- | --------------- | ------------------ |
| STAT | 获取邮箱统计信息       | `STAT`          | `STAT`             |
| LIST | 获取邮件列表           | `LIST [msg]`    | `LIST` 或 `LIST 1` |
| RETR | 获取完整邮件           | `RETR <msg>`    | `RETR 1`           |
| DELE | 标记邮件为删除         | `DELE <msg>`    | `DELE 1`           |
| NOOP | 无操作                 | `NOOP`          | `NOOP`             |
| RSET | 重置会话状态           | `RSET`          | `RSET`             |
| TOP  | 获取邮件头部和部分内容 | `TOP <msg> <n>` | `TOP 1 10`         |
| UIDL | 获取邮件唯一标识符     | `UIDL [msg]`    | `UIDL` 或 `UIDL 1` |
| QUIT | 结束会话并进入更新状态 | `QUIT`          | `QUIT`             |

## POP3响应

POP3响应由状态指示符（+OK或-ERR）和可选的服务器信息组成：

- **+OK**：表示命令执行成功
- **-ERR**：表示命令执行失败

多行响应以一个包含单个点(.)的行结束。

## 完整POP3会话示例

以下是完整的POP3会话示例，包括认证、查看邮件列表、获取邮件内容和断开连接：

```
C: <建立与服务器的TCP连接>
S: +OK POP3 server ready <<EMAIL>>

C: USER <EMAIL>
S: +OK

C: PASS password123
S: +OK Logged in.

C: STAT
S: +OK 2 320

C: LIST
S: +OK 2 messages (320 octets)
S: 1 120
S: 2 200
S: .

C: RETR 1
S: +OK 120 octets
S: From: <EMAIL>
S: To: <EMAIL>
S: Subject: Test Email
S: Date: Mon, 13 Jan 2023 12:15:23 -0500
S: 
S: This is the body of the first email.
S: .

C: DELE 1
S: +OK message 1 deleted

C: QUIT
S: +OK POP3 server signing off (1 message deleted)
```

## 安全POP3 (POP3S)

POP3可通过两种方式实现安全传输：

1. **隐式SSL**：直接在端口995上使用SSL/TLS加密连接
2. **显式SSL (STLS)**：在普通POP3会话中使用STLS命令升级为加密连接

STLS示例：

```
C: <建立与服务器的TCP连接>
S: +OK POP3 server ready
C: STLS
S: +OK Begin TLS negotiation
<此时进行TLS握手>
C: USER <EMAIL>
... 继续加密会话 ...
```

## POP3的UIDL命令

UIDL（Unique ID Listing）是POP3协议的一个重要扩展，它为每封邮件提供永久唯一的标识符，使客户端可以跟踪邮件状态：

```
C: UIDL
S: +OK
S: 1 9dcsfOZ31mRgABc2
S: 2 QLd9vJ1AijBoWxd1
S: .

C: UIDL 1
S: +OK 1 9dcsfOZ31mRgABc2
```

## 邮件服务提供商特殊要求

不同邮件服务商可能有特殊要求，以下是几个主要邮件服务的POP3配置：

### Gmail

- POP3服务器：pop.gmail.com
- 端口：995（SSL）
- 需要在Gmail设置中启用POP访问
- 有特定保留/删除策略选项

### QQ邮箱

- POP3服务器：pop.qq.com
- 端口：995（SSL）
- 需要使用授权码而非账户密码
- 在QQ邮箱设置中需启用POP3服务

### Outlook/Hotmail

- POP3服务器：outlook.office365.com
- 端口：995（SSL）
- 需要在Outlook设置中启用POP访问
- 可能有流量和频率限制

## POP3与IMAP对比

| 特性           | POP3                       | IMAP                       |
| -------------- | -------------------------- | -------------------------- |
| 主要用途       | 下载并本地存储邮件         | 在服务器上管理邮件         |
| 多设备同步     | 不支持（单设备模式）       | 完全支持                   |
| 服务器存储需求 | 低（邮件通常被下载后删除） | 高（所有邮件保留在服务器） |
| 离线访问       | 完全支持                   | 有限支持                   |
| 带宽使用       | 大量初始下载，之后较少     | 持续同步，带宽使用均匀     |
| 复杂度         | 简单                       | 复杂                       |

## 常见问题与解决方案

### 认证失败

- **问题**：-ERR Authentication failed
- **解决**：
  - 检查用户名和密码是否正确
  - 对特殊邮箱服务，使用授权码代替密码
  - 确认服务商是否已启用POP3访问

### 连接问题

- **问题**：无法连接到POP3服务器
- **解决**：
  - 检查服务器地址和端口是否正确
  - 检查网络连接和防火墙设置
  - 确认服务器支持POP3协议

### "已下载"邮件仍在服务器

- **问题**：同一邮件被重复下载
- **解决**：
  - 确认是否使用DELE命令标记删除
  - 检查客户端配置，是否设置了"下载后保留服务器副本"
  - 使用UIDL确保邮件唯一性

## 实现建议

### Python实现示例

```python
import poplib
from email import parser

def fetch_emails(server, port, username, password, use_ssl=True):
    # 连接到POP3服务器
    if use_ssl:
        mail_server = poplib.POP3_SSL(server, port)
    else:
        mail_server = poplib.POP3(server, port)
    
    try:
        # 打印服务器欢迎信息
        print(mail_server.getwelcome().decode('utf-8'))
        
        # 认证
        mail_server.user(username)
        mail_server.pass_(password)
        
        # 获取邮箱状态
        status = mail_server.stat()
        print(f'邮件数量: {status[0]}, 大小: {status[1]} bytes')
        
        # 获取邮件列表
        resp, items, octets = mail_server.list()
        print(f'邮件列表获取成功: {len(items)} 封邮件')
        
        # 获取最新邮件
        if len(items) > 0:
            # 获取最新邮件（最后一封）
            latest_id = len(items)
            resp, lines, octets = mail_server.retr(latest_id)
            
            # 解析邮件内容
            msg_content = b'\r\n'.join(lines).decode('utf-8')
            msg = parser.Parser().parsestr(msg_content)
            
            # 打印邮件信息
            print(f'From: {msg["from"]}')
            print(f'To: {msg["to"]}')
            print(f'Subject: {msg["subject"]}')
            
            # 可选：删除邮件
            # mail_server.dele(latest_id)
            print('获取邮件成功')
        
    except Exception as e:
        print(f'错误: {e}')
    finally:
        # 关闭连接并提交更改
        mail_server.quit()

# 使用示例
fetch_emails(
    server='pop.example.com',
    port=995,
    username='<EMAIL>',
    password='password123',
    use_ssl=True
)
```

## 参考资料

- [RFC 1939 - Post Office Protocol - Version 3](https://tools.ietf.org/html/rfc1939)
- [RFC 2449 - POP3 Extension Mechanism](https://tools.ietf.org/html/rfc2449)
- [RFC 2595 - Using TLS with IMAP, POP3 and ACAP](https://tools.ietf.org/html/rfc2595)
- [Python poplib 文档](https://docs.python.org/3/library/poplib.html) 