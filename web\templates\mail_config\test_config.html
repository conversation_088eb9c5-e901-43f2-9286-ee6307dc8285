{% extends "base.html" %} {% block title %}测试邮箱配置 - {{ app_name }}{%
endblock %} {% block content %}
<div class="container">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-flask me-2"></i>测试邮箱配置</h2>
        <div>
          <a
            href="{{ url_for('mail_config.index') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-arrow-left me-2"></i>返回配置页
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- 配置信息概要 -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h6 class="mb-0">
            <i class="fas fa-paper-plane me-2"></i>SMTP配置 (发送)
          </h6>
        </div>
        <div class="card-body">
          <table class="table table-sm table-borderless mb-0">
            <tr>
              <td><strong>服务器:</strong></td>
              <td>
                {{ current_user.smtp_server }}:{{ current_user.smtp_port }}
              </td>
            </tr>
            <tr>
              <td><strong>用户名:</strong></td>
              <td>{{ current_user.smtp_username }}</td>
            </tr>
            <tr>
              <td><strong>加密:</strong></td>
              <td>{{ 'TLS' if current_user.smtp_use_tls else 'SSL' }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h6 class="mb-0"><i class="fas fa-inbox me-2"></i>POP3配置 (接收)</h6>
        </div>
        <div class="card-body">
          <table class="table table-sm table-borderless mb-0">
            <tr>
              <td><strong>服务器:</strong></td>
              <td>
                {{ current_user.pop3_server }}:{{ current_user.pop3_port }}
              </td>
            </tr>
            <tr>
              <td><strong>用户名:</strong></td>
              <td>{{ current_user.pop3_username }}</td>
            </tr>
            <tr>
              <td><strong>加密:</strong></td>
              <td>{{ 'SSL' if current_user.pop3_use_ssl else '无' }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- 测试结果 -->
  {% if test_results %}
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-clipboard-check me-2"></i>测试结果
          </h5>
        </div>
        <div class="card-body">
          <div class="row">
            <!-- SMTP测试结果 -->
            <div class="col-md-6">
              <h6>SMTP发送测试</h6>
              {% if test_results.smtp.success %}
              <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>测试成功</strong><br />
                {{ test_results.smtp.message }}<br />
                <small
                  >服务器: {{ test_results.smtp.server }}:{{
                  test_results.smtp.port }}</small
                >
              </div>
              {% else %}
              <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                <strong>测试失败</strong><br />
                {{ test_results.smtp.error }}
              </div>
              {% endif %}
            </div>

            <!-- POP3测试结果 -->
            <div class="col-md-6">
              <h6>POP3接收测试</h6>
              {% if test_results.pop3.success %}
              <div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>
                <strong>测试成功</strong><br />
                {{ test_results.pop3.message }}<br />
                <small
                  >服务器: {{ test_results.pop3.server }}:{{
                  test_results.pop3.port }}</small
                >
              </div>
              {% else %}
              <div class="alert alert-danger">
                <i class="fas fa-times-circle me-2"></i>
                <strong>测试失败</strong><br />
                {{ test_results.pop3.error }}
              </div>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- 发送测试邮件 -->
  <div class="row">
    <div class="col-lg-8">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">
            <i class="fas fa-envelope-open-text me-2"></i>发送测试邮件
          </h5>
        </div>
        <div class="card-body">
          <form method="POST">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

            <div class="mb-3">
              <label for="to_address" class="form-label">收件人邮箱 *</label>
              {{ form.to_address(class="form-control") }}
              <div class="form-text">
                发送测试邮件到此邮箱地址以验证SMTP配置
              </div>
            </div>

            <div class="mb-3">
              <label for="subject" class="form-label">邮件主题 *</label>
              {{ form.subject(class="form-control") }}
            </div>

            <div class="mb-4">
              <label for="content" class="form-label">邮件内容 *</label>
              {{ form.content(class="form-control") }}
            </div>

            <div class="d-flex justify-content-between">
              <div>
                {{ form.submit(class="btn btn-primary") }}
                <a
                  href="{{ url_for('mail_config.advanced_setup') }}"
                  class="btn btn-outline-secondary ms-2"
                >
                  修改配置
                </a>
              </div>
              <div>
                <button
                  type="button"
                  class="btn btn-info"
                  onclick="testPOP3Connection()"
                >
                  <i class="fas fa-download me-2"></i>测试POP3连接
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 测试指南 -->
    <div class="col-lg-4">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0"><i class="fas fa-lightbulb me-2"></i>测试指南</h6>
        </div>
        <div class="card-body">
          <p><strong>测试流程：</strong></p>
          <ol class="small">
            <li>输入有效的收件人邮箱</li>
            <li>点击"发送测试邮件"</li>
            <li>检查收件箱是否收到测试邮件</li>
            <li>点击"测试POP3连接"验证接收功能</li>
          </ol>

          <p class="mt-3"><strong>测试内容：</strong></p>
          <ul class="small">
            <li><strong>SMTP测试:</strong> 验证邮件发送能力</li>
            <li><strong>POP3测试:</strong> 验证邮件接收能力</li>
            <li><strong>连接测试:</strong> 验证服务器连接性</li>
            <li><strong>认证测试:</strong> 验证用户凭据</li>
          </ul>

          <div class="alert alert-info mt-3">
            <i class="fas fa-info-circle me-2"></i>
            <small>
              <strong>提示:</strong>
              测试成功后，您就可以在Web界面中正常收发邮件了。
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- POP3测试模态框 -->
<div class="modal fade" id="pop3TestModal" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">POP3连接测试</h5>
        <button
          type="button"
          class="btn-close"
          data-bs-dismiss="modal"
        ></button>
      </div>
      <div class="modal-body">
        <div id="pop3TestResult">
          <div class="text-center">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">正在测试...</span>
            </div>
            <p class="mt-2">正在连接POP3服务器...</p>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          关闭
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  function testPOP3Connection() {
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById("pop3TestModal"));
    modal.show();

    // 重置结果区域
    document.getElementById("pop3TestResult").innerHTML = `
    <div class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">正在测试...</span>
      </div>
      <p class="mt-2">正在连接POP3服务器...</p>
    </div>
  `;

    // 发送AJAX请求测试POP3连接
    fetch('{{ url_for("mail_config.test_pop3_connection") }}', {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-CSRFToken": "{{ csrf_token() }}",
      },
    })
      .then((response) => response.json())
      .then((data) => {
        let resultHtml = "";
        if (data.success) {
          resultHtml = `
        <div class="alert alert-success">
          <i class="fas fa-check-circle me-2"></i>
          <strong>POP3连接测试成功！</strong><br>
          ${data.message}<br>
          <small>服务器: ${data.server}:${data.port}</small>
          ${
            data.email_count !== undefined
              ? `<br><small>邮箱中有 ${data.email_count} 封邮件</small>`
              : ""
          }
        </div>
      `;
        } else {
          resultHtml = `
        <div class="alert alert-danger">
          <i class="fas fa-times-circle me-2"></i>
          <strong>POP3连接测试失败</strong><br>
          ${data.error}
        </div>
      `;
        }
        document.getElementById("pop3TestResult").innerHTML = resultHtml;
      })
      .catch((error) => {
        document.getElementById("pop3TestResult").innerHTML = `
      <div class="alert alert-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <strong>测试请求失败</strong><br>
        ${error.message}
      </div>
    `;
      });
  }
</script>
{% endblock %}
