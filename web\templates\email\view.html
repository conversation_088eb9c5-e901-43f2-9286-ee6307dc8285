{% extends "base.html" %} {% block title %}{{ email.subject or '查看邮件' }} -
{{ app_name }}{% endblock %} {% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
          <i class="fas fa-envelope-open-text me-2"></i>邮件详情 {% if
          email.is_spam %}
          <span class="badge bg-danger ms-2">垃圾邮件</span>
          {% endif %}
        </h2>
        <div>
          <a
            href="{{ url_for('email.inbox') }}"
            class="btn btn-outline-secondary"
          >
            <i class="fas fa-arrow-left me-2"></i>返回收件箱
          </a>
          <div class="btn-group">
            <button
              type="button"
              class="btn btn-outline-primary dropdown-toggle"
              data-bs-toggle="dropdown"
            >
              <i class="fas fa-reply me-2"></i>操作
            </button>
            <ul class="dropdown-menu">
              <li>
                <a
                  class="dropdown-item"
                  href="{{ url_for('email.compose') }}?reply_to={{ email.message_id }}"
                >
                  <i class="fas fa-reply me-2"></i>回复
                </a>
              </li>
              <li>
                <a
                  class="dropdown-item"
                  href="{{ url_for('email.compose') }}?forward={{ email.message_id }}"
                >
                  <i class="fas fa-forward me-2"></i>转发
                </a>
              </li>
              <li><hr class="dropdown-divider" /></li>
              <li>
                {% if email.is_spam %}
                <a
                  class="dropdown-item"
                  href="{{ url_for('email.unmark_spam', message_id=email.message_id) }}"
                >
                  <i class="fas fa-check-circle me-2"></i>非垃圾邮件
                </a>
                {% else %}
                <a
                  class="dropdown-item text-warning"
                  href="{{ url_for('email.mark_spam', message_id=email.message_id) }}"
                >
                  <i class="fas fa-ban me-2"></i>标记为垃圾邮件
                </a>
                {% endif %}
              </li>
              <li>
                <a
                  class="dropdown-item text-danger"
                  href="{{ url_for('email.delete', message_id=email.message_id) }}"
                  onclick="return confirm('确定要删除此邮件吗？')"
                >
                  <i class="fas fa-trash me-2"></i>删除
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card email-content">
        <div class="card-body">
          <!-- 邮件头部信息 -->
          <div class="email-header mb-4">
            <h4 class="mb-1">{{ email.subject or '(无主题)' }}</h4>
            <hr />
            <div class="row">
              <div class="col-md-6">
                <p class="mb-1">
                  <strong>发件人:</strong> {{ email.from_addr }}
                </p>
                <p class="mb-1">
                  <strong>收件人:</strong> {{ email.to_addrs|join(', ') if
                  email.to_addrs else '' }}
                </p>
                {% if email.cc_addrs %}
                <p class="mb-1">
                  <strong>抄送:</strong> {{ email.cc_addrs|join(', ') }}
                </p>
                {% endif %}
              </div>
              <div class="col-md-6 text-md-end">
                <p class="mb-1">
                  <strong>接收时间:</strong>
                  {% if email.date %} {% if email.date.strftime %} {{
                  email.date.strftime('%Y-%m-%d %H:%M:%S') }} {% else %} {{
                  email.date }} {% endif %} {% else %} 未知 {% endif %}
                </p>
                <p class="mb-1">
                  <strong>大小:</strong> {{ (email.size / 1024)|round(2) }} KB
                </p>
                {% if not email.is_read %}
                <p class="mb-1">
                  <strong>状态:</strong>
                  <span class="badge bg-warning">未读</span>
                </p>
                {% endif %}
              </div>
            </div>
          </div>

          <!-- 邮件正文 -->
          <div class="email-body mb-4">
            {% if email.content %} {% if '<html' in email.content or '<body' in
            email.content %}
            <!-- 尝试渲染HTML，使用iframe进行沙箱化显示 -->
            <iframe
              srcdoc="{{ email.content }}"
              sandbox="allow-same-origin allow-popups allow-forms"
              style="width: 100%; height: 500px; border: 1px solid #ddd"
            >
            </iframe>
            {% else %}
            <!-- 纯文本内容 -->
            <pre style="white-space: pre-wrap; word-wrap: break-word">
{{ email.content }}</pre
            >
            {% endif %} {% else %}
            <p class="text-muted">(邮件内容为空)</p>
            {% endif %}
          </div>

          <!-- 附件列表 -->
          {% if email.has_attachments and email.attachments %}
          <hr />
          <div class="attachments-section mt-4">
            <h5>
              <i class="fas fa-paperclip me-2"></i>附件 ({{
              email.attachments|length }})
            </h5>
            <div class="d-flex flex-wrap">
              {% for attachment in email.attachments %}
              <a
                href="{{ url_for('email.download_attachment', message_id=email.message_id, filename=attachment.filename) }}"
                class="attachment-item"
                download
              >
                <i class="fas fa-file me-1"></i> {{ attachment.filename }}
                <small class="text-muted"
                  >({{ (attachment.size / 1024)|round(2) }} KB)</small
                >
              </a>
              {% endfor %}
            </div>
          </div>
          {% endif %}
        </div>

        <div class="card-footer bg-light">
          <div class="d-flex justify-content-end">
            <a
              href="{{ url_for('email.compose') }}?reply_to={{ email.message_id }}"
              class="btn btn-primary me-2"
            >
              <i class="fas fa-reply me-2"></i>回复
            </a>
            <a
              href="{{ url_for('email.compose') }}?forward={{ email.message_id }}"
              class="btn btn-secondary"
            >
              <i class="fas fa-forward me-2"></i>转发
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
