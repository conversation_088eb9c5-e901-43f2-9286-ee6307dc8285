{% extends "base.html" %} {% block title %}个人资料 - {{ app_name }}{% endblock
%} {% block content %}
<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h4 class="mb-0"><i class="fas fa-user-cog me-2"></i>个人资料</h4>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h5>基本信息</h5>
              <table class="table table-borderless">
                <tr>
                  <td><strong>用户名:</strong></td>
                  <td>{{ current_user.username }}</td>
                </tr>
                <tr>
                  <td><strong>邮箱地址:</strong></td>
                  <td>{{ current_user.email }}</td>
                </tr>
                <tr>
                  <td><strong>真实姓名:</strong></td>
                  <td>{{ current_user.full_name or '未设置' }}</td>
                </tr>
                <tr>
                  <td><strong>账户状态:</strong></td>
                  <td>
                    {% if current_user.is_active %}
                    <span class="badge bg-success">已激活</span>
                    {% else %}
                    <span class="badge bg-danger">已禁用</span>
                    {% endif %}
                  </td>
                </tr>
                <tr>
                  <td><strong>创建时间:</strong></td>
                  <td>
                    {% if current_user.created_at %} {% if
                    current_user.created_at.strftime %} {{
                    current_user.created_at.strftime('%Y-%m-%d %H:%M:%S') }} {%
                    else %} {{ current_user.created_at }} {% endif %} {% else %}
                    未知 {% endif %}
                  </td>
                </tr>
                <tr>
                  <td><strong>最后登录:</strong></td>
                  <td>
                    {% if current_user.last_login %} {% if
                    current_user.last_login.strftime %} {{
                    current_user.last_login.strftime('%Y-%m-%d %H:%M:%S') }} {%
                    else %} {{ current_user.last_login }} {% endif %} {% else %}
                    未知 {% endif %}
                  </td>
                </tr>
              </table>
            </div>
            <div class="col-md-6">
              <h5>账户操作</h5>
              <div class="d-grid gap-2">
                <a
                  href="{{ url_for('main.dashboard') }}"
                  class="btn btn-outline-primary"
                >
                  <i class="fas fa-tachometer-alt me-2"></i>返回仪表板
                </a>
                <a
                  href="{{ url_for('email.inbox') }}"
                  class="btn btn-outline-info"
                >
                  <i class="fas fa-inbox me-2"></i>查看收件箱
                </a>
                <a
                  href="{{ url_for('email.compose') }}"
                  class="btn btn-outline-success"
                >
                  <i class="fas fa-edit me-2"></i>写邮件
                </a>
                <hr />
                <a
                  href="{{ url_for('auth.logout') }}"
                  class="btn btn-outline-danger"
                >
                  <i class="fas fa-sign-out-alt me-2"></i>退出登录
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="card mt-4">
        <div class="card-header">
          <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>系统信息</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <p><strong>应用版本:</strong> {{ app_version }}</p>
              <p><strong>运行环境:</strong> 开发模式</p>
            </div>
            <div class="col-md-6">
              <p><strong>支持的功能:</strong></p>
              <ul class="list-unstyled">
                <li><i class="fas fa-check text-success me-1"></i> 邮件收发</li>
                <li>
                  <i class="fas fa-check text-success me-1"></i> 垃圾邮件过滤
                </li>
                <li><i class="fas fa-check text-success me-1"></i> 附件支持</li>
                <li><i class="fas fa-check text-success me-1"></i> HTML邮件</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
