{% extends "base.html" %} {% block title %}发件箱 - {{ app_name }}{% endblock %}
{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
          <i class="fas fa-paper-plane me-2"></i>发件箱 {% if total > 0 %}
          <small class="text-muted">({{ total }} 封邮件)</small>
          {% endif %}
        </h2>
        <div>
          <a href="{{ url_for('email.compose') }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>写邮件
          </a>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-body p-0">
          {% if emails %} {% for email in emails %}
          <div class="email-item">
            <div class="row align-items-center">
              <div class="col-md-1">
                <input
                  type="checkbox"
                  class="form-check-input"
                  value="{{ email.message_id }}"
                />
              </div>
              <div class="col-md-3">
                <strong
                  >{{ email.to_addrs|join(', ') if email.to_addrs else
                  '(无收件人)' }}</strong
                >
                {% if email.cc_addrs %}
                <br /><small class="text-muted"
                  >抄送: {{ email.cc_addrs|join(', ') }}</small
                >
                {% endif %} {% if email.has_attachments %}
                <i class="fas fa-paperclip text-muted ms-2" title="有附件"></i>
                {% endif %}
              </div>
              <div class="col-md-6">
                <a
                  href="{{ url_for('email.view_sent', message_id=email.message_id) }}"
                  class="text-decoration-none text-dark"
                >
                  {{ email.subject or '(无主题)' }}
                </a>
                {% if email.status %}
                <span class="badge bg-info ms-2">{{ email.status }}</span>
                {% endif %}
              </div>
              <div class="col-md-2 text-end">
                <small class="text-muted">
                  {% if email.date %} {{ email.date.strftime('%m-%d %H:%M') }}
                  {% endif %}
                </small>
                <div class="btn-group" role="group">
                  <button
                    type="button"
                    class="btn btn-sm btn-outline-secondary dropdown-toggle"
                    data-bs-toggle="dropdown"
                  >
                    操作
                  </button>
                  <ul class="dropdown-menu">
                    <li>
                      <a
                        class="dropdown-item"
                        href="{{ url_for('email.view_sent', message_id=email.message_id) }}"
                      >
                        <i class="fas fa-eye me-2"></i>查看
                      </a>
                    </li>
                    <li>
                      <a
                        class="dropdown-item"
                        href="{{ url_for('email.compose') }}?reply_to={{ email.message_id }}"
                      >
                        <i class="fas fa-forward me-2"></i>转发
                      </a>
                    </li>
                    <li><hr class="dropdown-divider" /></li>
                    <li>
                      <a
                        class="dropdown-item text-danger"
                        href="{{ url_for('email.delete_sent', message_id=email.message_id) }}"
                        onclick="return confirm('确定要删除此邮件吗？')"
                      >
                        <i class="fas fa-trash me-2"></i>删除
                      </a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          {% endfor %} {% else %}
          <div class="text-center py-5">
            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">发件箱为空</h5>
            <p class="text-muted">您还没有发送任何邮件</p>
            <a href="{{ url_for('email.compose') }}" class="btn btn-primary">
              <i class="fas fa-edit me-2"></i>立即写邮件
            </a>
          </div>
          {% endif %}
        </div>
      </div>

      <!-- 分页 -->
      {% if total > per_page %}
      <nav aria-label="邮件分页" class="mt-4">
        <ul class="pagination justify-content-center">
          {% set total_pages = (total + per_page - 1) // per_page %} {% if page
          > 1 %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('email.sent', page=page-1) }}"
              >上一页</a
            >
          </li>
          {% endif %} {% for p in range(1, total_pages + 1) %} {% if p == page
          %}
          <li class="page-item active">
            <span class="page-link">{{ p }}</span>
          </li>
          {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 2 and p <= page
          + 2) %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('email.sent', page=p) }}"
              >{{ p }}</a
            >
          </li>
          {% elif p == 4 or p == total_pages - 3 %}
          <li class="page-item disabled">
            <span class="page-link">...</span>
          </li>
          {% endif %} {% endfor %} {% if page < total_pages %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('email.sent', page=page+1) }}"
              >下一页</a
            >
          </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
