# 计算机网络课程大作业事项

## 1. 分数占比

大作业分数占期末总评的 **30%**。大作业内分数分为基础分+附加分两部分：
*   **基础分占比 75%**：为必做内容的完成度。
*   **附加分占比 25%**：为扩展内容的完成度，扩展内容鼓励多做，**至少选做2项**。

## 2. 开展形式

同学们自由组队，推荐为 **4-5 人每组**。本次大作业共 **10 个题目**，每个题目供 **2 个组**选择。

选题链接模板：[https://kdocs.cn/l/cgXRMKXpbQnz](https://kdocs.cn/l/cgXRMKXpbQnz) (切勿覆盖他人已填表格!!!!!)

**16 周课上 poster 展示，现场打分。**

## 3. 提交内容

主要分为两部分：

1.  **项目海报**：参照给出的学术会议 poster 形式，总结自己组的工作内容，制作一份海报。
2.  **一份提交文件**：包括项目报告文档、代码，以及可能有的演示材料，如截图、视频等，具体在每道题后面都有要求。

---

# 题目五 —— 基于 SMTP/POP3 协议的电子邮件客户端开发与安全机制实现

## 实验目标

1.  掌握 SMTP 协议发送邮件与 POP3/IMAP 协议接收邮件的核心流程；
2.  实现 MIME 协议对邮件正文与附件的编码/解码；
3.  集成 SSL/TLS 加密机制保障邮件传输安全性；
4.  设计垃圾邮件过滤系统（基于关键词或贝叶斯分类）。

## 实验要求

### 1. 基础功能（必做）

#### 客户端功能

*   **邮件发送**：通过 SMTP 协议发送纯文本/HTML 邮件，支持添加附件（如图片、文档）；
*   **邮件接收**：通过 POP3 协议获取邮箱邮件列表并下载内容；
*   **用户认证**：支持明文密码与 SSL 加密登录（如 LOGIN 与 AUTH PLAIN 命令）；
*   **本地存储**：将接收的邮件保存为 `.eml` 格式文件。

#### 服务端功能

*   **模拟邮件服务器**：使用 Python 的 `smtpd` 模块实现基础 SMTP 服务；
*   **支持多用户并发连接**（至少 10 个客户端同时操作）；
*   **邮件存储**：使用 SQLite 数据库保存邮件元数据（发件人、收件人、时间戳）。

### 2. 扩展功能（选做，任选 2 项）

*   **端到端加密**：使用 PGP 对邮件正文进行加密签名；
*   **垃圾邮件过滤**：基于关键词匹配或贝叶斯分类器标记垃圾邮件；
*   **邮件撤回功能**：通过唯一邮件 ID 实现服务器端邮件撤回；
*   **Web 邮件界面**：使用 Flask/Django 开发浏览器端邮件管理系统。

## 技术栈要求

*   **协议标准**：SMTP (RFC 5321)、POP3 (RFC 1939)、MIME (RFC 2045)；
*   **编程语言**：Python (推荐使用 `imaplib` 库)；
*   **服务端实现**：`aiosmtpd` 包（替代已弃用的 `smtpd` 模块）；
*   **安全机制**：SSL/TLS、PGP (GnuPG 库)；
*   **数据存储**：SQLite (邮件元数据)、文件系统 (`.eml` 原始邮件)。

## 分工建议

*（请参考之前的详细分工建议进行填写或调整）*

## 实验提交内容

### 1. 代码

*   客户端完整源码（支持发送/接收/附件功能）；
*   服务端脚本（含并发处理与数据库操作）；
*   扩展功能代码。

### 2. 文档

*   **设计文档**：协议交互流程图、MIME 结构示例、SSL 握手过程；
*   **测试报告**：并发压力测试结果（如 50 用户同时发送邮件）；
*   **用户手册**：客户端配置说明（服务器 IP、端口、SSL 证书导入）。

### 3. 演示

*   发送带附件的邮件并展示接收端完整解析；
*   模拟中间人攻击，展示 SSL 加密防护效果。